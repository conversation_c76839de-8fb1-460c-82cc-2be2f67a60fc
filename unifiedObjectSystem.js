/**
 * ZUNIFIKOWANY SYSTEM ZARZĄDZANIA OBIEKTAMI
 * Rozwiązuje konflikty między objects.js a atheroscleroticPlaques.js
 */

// ===== CENTRALNY REJESTRATOR OBIEKTÓW =====
class UnifiedObjectManager {
    constructor() {
        this.occupiedRegions = new Map(); // Mapa zajętych regionów
        this.objectRegistry = {
            eggs: [],
            obstacles: [],
            wallPlaques: [],
            atheroscleroticPlaques: []
        };
        this.spawnThrottles = {
            egg: 0,
            obstacle: 0,
            wallCube: 0,
            plaqueCluster: 0
        };
        this.config = {
            THROTTLE_MS: 100,
            MIN_OBJECT_DISTANCE: 2.0,  // Minimalna odległość między obiektami
            REGION_SIZE: 3.0,          // Rozmiar regionu dla sprawdzania kolizji
            MAX_OBJECTS_PER_REGION: 5  // Maksymalna liczba obiektów w regionie
        };
    }

    /**
     * Sprawdza czy region jest wolny do umieszczenia obiektu
     */
    isRegionAvailable(position, objectSize) {
        const regionKey = this.getRegionKey(position);
        const objectsInRegion = this.occupiedRegions.get(regionKey) || [];

        // Sprawdź limit obiektów w regionie
        if (objectsInRegion.length >= this.config.MAX_OBJECTS_PER_REGION) {
            return false;
        }

        // Sprawdź odległość od innych obiektów
        for (let obj of objectsInRegion) {
            const distance = BABYLON.Vector3.Distance(position, obj.position);
            const minDistance = this.config.MIN_OBJECT_DISTANCE + objectSize + obj.size;

            if (distance < minDistance) {
                return false;
            }
        }

        return true;
    }

    /**
     * Rejestruje obiekt w systemie
     */
    registerObject(type, position, size, meshRef) {
        const regionKey = this.getRegionKey(position);
        const objectData = {
            type,
            position: position.clone(),
            size,
            mesh: meshRef,
            timestamp: performance.now()
        };

        // Dodaj do rejestru typu
        this.objectRegistry[type].push(objectData);

        // Dodaj do mapy regionów
        if (!this.occupiedRegions.has(regionKey)) {
            this.occupiedRegions.set(regionKey, []);
        }
        this.occupiedRegions.get(regionKey).push(objectData);

        return objectData;
    }

    /**
     * Usuwa obiekt z systemu
     */
    unregisterObject(objectData) {
        // Usuń z rejestru typu
        const typeArray = this.objectRegistry[objectData.type];
        const typeIndex = typeArray.indexOf(objectData);
        if (typeIndex > -1) {
            typeArray.splice(typeIndex, 1);
        }

        // Usuń z mapy regionów
        const regionKey = this.getRegionKey(objectData.position);
        const regionArray = this.occupiedRegions.get(regionKey);
        if (regionArray) {
            const regionIndex = regionArray.indexOf(objectData);
            if (regionIndex > -1) {
                regionArray.splice(regionIndex, 1);
            }

            // Usuń pusty region
            if (regionArray.length === 0) {
                this.occupiedRegions.delete(regionKey);
            }
        }
    }

    /**
     * Sprawdza throttling dla danego typu obiektu
     */
    canSpawn(type) {
        const now = performance.now();
        const lastSpawn = this.spawnThrottles[type] || 0;

        if (now - lastSpawn < this.config.THROTTLE_MS) {
            return false;
        }

        this.spawnThrottles[type] = now;
        return true;
    }

    /**
     * Generuje klucz regionu na podstawie pozycji
     */
    getRegionKey(position) {
        const regionSize = this.config.REGION_SIZE;
        const x = Math.floor(position.x / regionSize);
        const y = Math.floor(position.y / regionSize);
        const z = Math.floor(position.z / regionSize);
        return `${x},${y},${z}`;
    }

    /**
     * Znajduje bezpieczną pozycję dla obiektu
     */
    findSafePosition(section, objectSize, maxAttempts = 10) {
        for (let attempt = 0; attempt < maxAttempts; attempt++) {
            let position;

            // Wybierz strategię pozycjonowania
            const strategy = Math.random();

            if (strategy < 0.4) {
                // Strategia sferyczna (kompatybilna z atherosclerotic)
                position = this.generateSphericalPosition(section, objectSize);
            } else if (strategy < 0.8) {
                // Strategia cylindryczna (kompatybilna z objects.js)
                position = this.generateCylindricalPosition(section, objectSize);
            } else {
                // Strategia warstwowa
                position = this.generateLayeredPosition(section, objectSize);
            }

            // Sprawdź czy pozycja jest bezpieczna
            if (this.isRegionAvailable(position, objectSize)) {
                return position;
            }
        }

        console.warn("Nie udało się znaleźć bezpiecznej pozycji dla obiektu");
        return null;
    }

    /**
     * Generuje pozycję w stylu sferycznym (atherosclerotic)
     */
    generateSphericalPosition(section, objectSize) {
        const phi = Math.random() * Math.PI * 2;
        const theta = Math.acos(2 * Math.random() - 1);
        const maxRadius = section.radius * 0.7; // 70% promienia
        const radius = Math.random() * maxRadius;

        const x = section.centerPoint.x + radius * Math.sin(theta) * Math.cos(phi);
        const y = section.centerPoint.y + radius * Math.sin(theta) * Math.sin(phi);
        const z = section.centerPoint.z + radius * Math.cos(theta) * 0.3; // Ograniczony Z

        return new BABYLON.Vector3(x, y, z);
    }

    /**
     * Generuje pozycję w stylu cylindrycznym (objects.js)
     */
    generateCylindricalPosition(section, objectSize) {
        const angle = Math.random() * Math.PI * 2;
        const radius = section.radius * Math.random() * 0.7;
        const zOffset = (Math.random() * 2 - 1) * section.radius * 0.3;

        return new BABYLON.Vector3(
            section.centerPoint.x + Math.cos(angle) * radius,
            section.centerPoint.y + Math.sin(angle) * radius,
            section.centerPoint.z + zOffset
        );
    }

    /**
     * Generuje pozycję warstwową
     */
    generateLayeredPosition(section, objectSize) {
        const layer = Math.floor(Math.random() * 3); // 3 warstwy
        const angle = Math.random() * Math.PI * 2;
        const layerRadius = section.radius * (0.3 + layer * 0.2); // 30%, 50%, 70%

        return new BABYLON.Vector3(
            section.centerPoint.x + Math.cos(angle) * layerRadius,
            section.centerPoint.y + Math.sin(angle) * layerRadius,
            section.centerPoint.z + (layer - 1) * 0.5 // Rozłożone w Z
        );
    }

    /**
     * Czyści stare obiekty (garbage collection)
     */
    cleanup() {
        const now = performance.now();
        const maxAge = 30000; // 30 sekund

        for (let [regionKey, objects] of this.occupiedRegions.entries()) {
            for (let i = objects.length - 1; i >= 0; i--) {
                const obj = objects[i];

                // Sprawdź czy obiekt istnieje i nie jest usunięty
                if (!obj.mesh || obj.mesh.isDisposed()) {
                    objects.splice(i, 1);
                    continue;
                }

                // Sprawdź wiek obiektu
                if (now - obj.timestamp > maxAge) {
                    console.log(`Usuwanie starego obiektu typu ${obj.type}`);
                    this.unregisterObject(obj);
                }
            }

            // Usuń pusty region
            if (objects.length === 0) {
                this.occupiedRegions.delete(regionKey);
            }
        }
    }

    /**
     * Raportuje stan systemu
     */
    getStatus() {
        const totalObjects = Object.values(this.objectRegistry)
            .reduce((sum, arr) => sum + arr.length, 0);

        return {
            totalObjects,
            regions: this.occupiedRegions.size,
            byType: Object.fromEntries(
                Object.entries(this.objectRegistry)
                    .map(([type, arr]) => [type, arr.length])
            )
        };
    }
}

// ===== ZUNIFIKOWANE FUNKCJE POMOCNICZE =====

/**
 * Zunifikowana funkcja do obliczania pozycji w tunelu
 * Zastępuje zarówno calculatePositionInTunnel z objects.js jak i funkcje z atherosclerotic
 */
function calculateUnifiedPositionInTunnel(targetZ, objectSize = 0.5, objectType = 'generic') {
    if (!window.unifiedObjectManager) {
        window.unifiedObjectManager = new UnifiedObjectManager();
    }

    const section = findSectionAtPosition(targetZ);
    if (!section) {
        console.error("Nie można znaleźć sekcji tunelu dla Z:", targetZ);
        return null;
    }

    // Znajdź bezpieczną pozycję
    const position = window.unifiedObjectManager.findSafePosition(section, objectSize);

    if (!position) {
        // Fallback do podstawowej pozycji jeśli nie można znaleźć bezpiecznej
        const angle = Math.random() * Math.PI * 2;
        const radius = section.radius * 0.5; // Bliżej środka dla bezpieczeństwa

        return new BABYLON.Vector3(
            section.centerPoint.x + Math.cos(angle) * radius,
            section.centerPoint.y + Math.sin(angle) * radius,
            targetZ
        );
    }

    return position;
}

/**
 * Zunifikowana funkcja spawnu jajka
 */
function spawnEggUnified() {
    if (!window.unifiedObjectManager) {
        window.unifiedObjectManager = new UnifiedObjectManager();
    }

    // Sprawdź throttling
    if (!window.unifiedObjectManager.canSpawn('egg')) {
        return null;
    }

    if (!bunnyCollider || !tunnelSections || tunnelSections.length === 0 || !scene) {
        return null;
    }

    // Oblicz pozycję spawnu
    const distance = Config.EGG_SPAWN_MIN_DIST + 
        Math.random() * (Config.EGG_SPAWN_MAX_DIST - Config.EGG_SPAWN_MIN_DIST);
    const targetZ = bunnyCollider.position.z - distance;

    // Znajdź sekcję i oblicz rozmiar
    const section = findSectionAtPosition(targetZ);
    if (!section) return null;

    const diameter = section.radius * 0.18;
    const position = calculateUnifiedPositionInTunnel(targetZ, diameter, 'egg');

    if (!position) return null;

    // Utwórz jajko
    const egg = BABYLON.MeshBuilder.CreateSphere("egg", {
        diameter: diameter,
        segments: 12
    }, scene);

    egg.position = position;

    // Materiał
    const eggMaterial = new BABYLON.StandardMaterial("eggMat", scene);
    eggMaterial.diffuseColor = new BABYLON.Color3(1, 0.8, 0.2);
    eggMaterial.emissiveColor = new BABYLON.Color3(0.5, 0.4, 0.1);
    egg.material = eggMaterial;

    // Światło
    const eggLight = new BABYLON.PointLight("eggLight", position, scene);
    eggLight.diffuse = new BABYLON.Color3(1, 0.8, 0.2);
    eggLight.intensity = 0.6;
    eggLight.range = 3;

    // Zarejestruj w systemie
    const objectData = window.unifiedObjectManager.registerObject('eggs', position, diameter, egg);

    // Dodaj do globalnej tablicy (kompatybilność)
    if (!window.eggs) window.eggs = [];
    const eggData = {
        mesh: egg,
        light: eggLight,
        returnToCenter: false,
        _unifiedData: objectData
    };
    window.eggs.push(eggData);

    return eggData;
}

/**
 * Zunifikowana funkcja spawnu przeszkód
 */
function spawnObstacleUnified() {
    if (!window.unifiedObjectManager) {
        window.unifiedObjectManager = new UnifiedObjectManager();
    }

    // Sprawdź throttling
    if (!window.unifiedObjectManager.canSpawn('obstacle')) {
        return null;
    }

    if (!bunnyCollider || !tunnelSections || tunnelSections.length === 0 || !scene) {
        return null;
    }

    // Oblicz pozycję spawnu
    const distance = Config.OBSTACLE_SPAWN_MIN_DIST + 
        Math.random() * (Config.OBSTACLE_SPAWN_MAX_DIST - Config.OBSTACLE_SPAWN_MIN_DIST);
    const targetZ = bunnyCollider.position.z - distance;

    // Znajdź sekcję i oblicz rozmiar
    const section = findSectionAtPosition(targetZ);
    if (!section) return null;

    const diameter = section.radius * 0.22;
    const position = calculateUnifiedPositionInTunnel(targetZ, diameter, 'obstacle');

    if (!position) return null;

    // Utwórz przeszkodę
    const obstacle = BABYLON.MeshBuilder.CreateSphere("obstacle", {
        diameter: diameter,
        segments: 16
    }, scene);

    obstacle.position = position;

    // Materiał
    const obstacleMaterial = new BABYLON.StandardMaterial("obstacleMat", scene);
    obstacleMaterial.diffuseColor = new BABYLON.Color3(0.9, 0.3, 0.3);
    obstacleMaterial.emissiveColor = new BABYLON.Color3(0.4, 0.1, 0.1);
    obstacle.material = obstacleMaterial;

    // Światło
    const obstacleLight = new BABYLON.PointLight("obstacleLight", position, scene);
    obstacleLight.diffuse = new BABYLON.Color3(0.9, 0.3, 0.3);
    obstacleLight.intensity = 0.7;
    obstacleLight.range = 4;

    // Fizyka
    if (typeof BABYLON.PhysicsImpostor !== 'undefined') {
        obstacle.physicsImpostor = new BABYLON.PhysicsImpostor(
            obstacle,
            BABYLON.PhysicsImpostor.SphereImpostor,
            { mass: 0.1, restitution: 0.2 },
            scene
        );
    }

    // Zarejestruj w systemie
    const objectData = window.unifiedObjectManager.registerObject('obstacles', position, diameter, obstacle);

    // Dodaj do globalnej tablicy (kompatybilność)
    if (!window.obstacles) window.obstacles = [];
    const obstacleData = {
        mesh: obstacle,
        light: obstacleLight,
        isDestructible: Math.random() > 0.2,
        _unifiedData: objectData
    };
    window.obstacles.push(obstacleData);

    return obstacleData;
}

/**
 * Zunifikowana funkcja generowania zmian miażdżycowych
 */
function generateAtheroscleroticPlaquesUnified(tunnelSections, gameLevel, scene) {
    if (!window.unifiedObjectManager) {
        window.unifiedObjectManager = new UnifiedObjectManager();
    }

    if (!tunnelSections || tunnelSections.length === 0 || !scene) {
        console.error("Nie można wygenerować zmian miażdżycowych - brak sekcji tunelu lub sceny");
        return;
    }

    // Wyczyść istniejące zmiany miażdżycowe
    clearAtheroscleroticPlaques();

    // Określ liczbę klastrów
    let clustersCount;
    if (gameLevel <= 6) {
        clustersCount = PLAQUE_CONFIG.CLUSTERS_PER_LEVEL.EASY;
    } else if (gameLevel <= 12) {
        clustersCount = PLAQUE_CONFIG.CLUSTERS_PER_LEVEL.MEDIUM;
    } else {
        clustersCount = PLAQUE_CONFIG.CLUSTERS_PER_LEVEL.HARD;
    }

    console.log(`Generowanie ${clustersCount} klastrów zmian miażdżycowych dla poziomu ${gameLevel}`);

    // Wybierz sekcje z wykluczeniem 10% początku i końca (kompatybilność z oryginalnym kodem)
    const validSectionIndices = [];
    const startExcludeCount = Math.floor(tunnelSections.length * 0.1);
    const endExcludeCount = Math.floor(tunnelSections.length * 0.1);

    for (let i = startExcludeCount; i < tunnelSections.length - endExcludeCount; i++) {
        validSectionIndices.push(i);
    }

    shuffleArray(validSectionIndices);
    const clusterSectionIndices = validSectionIndices.slice(0, clustersCount);

    // Generuj klastry z throttling'iem dla wydajności
    let clustersGenerated = 0;

    function generateNextCluster() {
        if (clustersGenerated >= clusterSectionIndices.length) {
            console.log(`Wygenerowano ${atheroscleroticPlaques.length} zmian miażdżycowych w ${clustersCount} klastrach`);
            return;
        }

        // Sprawdź throttling
        if (window.unifiedObjectManager.canSpawn('plaqueCluster')) {
            const sectionIndex = clusterSectionIndices[clustersGenerated];
            const section = tunnelSections[sectionIndex];

            const plaquesInCluster = Math.floor(
                PLAQUE_CONFIG.PLAQUES_PER_CLUSTER.MIN +
                Math.random() * (PLAQUE_CONFIG.PLAQUES_PER_CLUSTER.MAX - PLAQUE_CONFIG.PLAQUES_PER_CLUSTER.MIN + 1)
            );

            generatePlaqueClusterUnified(section, plaquesInCluster, scene);
            clustersGenerated++;
        }

        // Schedułuj następny klaster
        setTimeout(generateNextCluster, 50); // 50ms delay dla wydajności
    }

    generateNextCluster();
}

/**
 * Zunifikowana funkcja generowania klastra blaszek
 */
function generatePlaqueClusterUnified(section, plaquesCount, scene) {
    if (!section || !section.centerPoint || !section.radius) {
        console.warn("Nieprawidłowa sekcja tunelu dla klastra zmian miażdżycowych");
        return;
    }

    // KLUCZOWA ZMIANA: Użyj oryginalnych funkcji 3D z atheroscleroticPlaques.js
    // zamiast tworzenia płaskich blaszek

    // 1. Wybierz typ rozmieszczenia klastra
    const clusterType = Math.random();
    let plaquePositions = [];

    if (clusterType < 0.4) {
        // Typ 1: Rozmieszczenie sferyczne (40% szans)
        if (typeof generateSphericalCluster === 'function') {
            plaquePositions = generateSphericalCluster(section, plaquesCount);
        }
    } else if (clusterType < 0.7) {
        // Typ 2: Rozmieszczenie elipsoidalne (30% szans)
        if (typeof generateEllipsoidalCluster === 'function') {
            plaquePositions = generateEllipsoidalCluster(section, plaquesCount);
        }
    } else {
        // Typ 3: Rozmieszczenie warstwowe/spiralne (30% szans)
        if (typeof generateLayeredCluster === 'function') {
            plaquePositions = generateLayeredCluster(section, plaquesCount);
        }
    }

    // Fallback jeśli funkcje 3D nie są dostępne
    if (plaquePositions.length === 0) {
        console.warn("Funkcje 3D nie są dostępne, używam zunifikowanego systemu pozycjonowania");
        for (let i = 0; i < plaquesCount; i++) {
            const baseDiameter = PLAQUE_CONFIG.SIZE.MIN + 
                Math.random() * (PLAQUE_CONFIG.SIZE.MAX - PLAQUE_CONFIG.SIZE.MIN);

            const tunnelSizeFactor = Math.min(1.2, section.radius / 2.0);
            const plaqueDiameter = baseDiameter * tunnelSizeFactor;

            // Znajdź pozycję z unified system
            const position = window.unifiedObjectManager.findSafePosition(section, plaqueDiameter);

            if (position) {
                const plaqueType = getRandomPlaqueType();
                createPlaqueUnified(position, plaqueDiameter, plaqueType, scene);
            }
        }
        return;
    }

    // 2. Utwórz zmiany miażdżycowe na wygenerowanych pozycjach 3D
    for (let i = 0; i < plaquePositions.length; i++) {
        const position = plaquePositions[i];

        // Losowy rozmiar zmiany z lepszą kontrolą proporcji
        const baseDiameter = PLAQUE_CONFIG.SIZE.MIN +
            Math.random() * (PLAQUE_CONFIG.SIZE.MAX - PLAQUE_CONFIG.SIZE.MIN);

        // Dostosuj rozmiar do wielkości tunelu dla lepszych proporcji
        const tunnelSizeFactor = Math.min(1.2, section.radius / 2.0);
        const plaqueDiameter = baseDiameter * tunnelSizeFactor;

        // Losowy typ zmiany miażdżycowej
        const plaqueType = getRandomPlaqueType();

        // Utwórz zmianę miażdżycową
        createPlaqueUnified(position, plaqueDiameter, plaqueType, scene);
    }
}

/**
 * Zunifikowana funkcja tworzenia blaszki (używa oryginalną createPlaque ale z rejestracją)
 */
function createPlaqueUnified(position, size, type, scene) {
    // Użyj oryginalnej funkcji createPlaque z atheroscleroticPlaques.js
    createPlaque(position, size, type, scene);

    // Zarejestruj w unified system
    if (atheroscleroticPlaques.length > 0) {
        const lastPlaque = atheroscleroticPlaques[atheroscleroticPlaques.length - 1];
        if (lastPlaque && lastPlaque.mesh) {
            // KLUCZOWA ZMIANA: Upewnij się, że parametry animacji są zainicjalizowane
            if (!lastPlaque.animParams) {
                lastPlaque.animParams = {
                    rotationSpeed: 0.0005 + Math.random() * 0.001,
                    pulseFreq: 0.0005 + Math.random() * 0.001,
                    pulseAmp: 0.03 + Math.random() * 0.04,
                    wobbleFreq: 0.0007 + Math.random() * 0.0006,
                    wobbleAmp: 0.03 + Math.random() * 0.03,
                    phaseOffset: Math.random() * Math.PI * 2,
                    originalScale: lastPlaque.mesh.scaling.clone()
                };
            }

            // Upewnij się, że animacja jest włączona
            window.animationEnabled = true;

            window.unifiedObjectManager.registerObject(
                'atheroscleroticPlaques', 
                position, 
                size, 
                lastPlaque.mesh
            );

            // Zapisz oryginalną pozycję dla animacji
            if (!lastPlaque.originalPosition) {
                lastPlaque.originalPosition = lastPlaque.mesh.position.clone();
            }
        }
    }
}

// ===== INICJALIZACJA SYSTEMU =====

/**
 * Inicjalizuje zunifikowany system zarządzania obiektami
 */
function initializeUnifiedObjectSystem() {
    console.log("Inicjalizacja zunifikowanego systemu zarządzania obiektami...");

    // Utwórz globalny manager
    if (!window.unifiedObjectManager) {
        window.unifiedObjectManager = new UnifiedObjectManager();
    }

    // Zastąp oryginalne funkcje
    window.originalSpawnEgg = window.spawnEgg;
    window.originalSpawnObstacle = window.spawnObstacle;
    window.originalGenerateAtheroscleroticPlaques = window.generateAtheroscleroticPlaques;
    window.originalCalculatePositionInTunnel = window.calculatePositionInTunnel;

    // Nowe zunifikowane funkcje
    window.spawnEgg = spawnEggUnified;
    window.spawnObstacle = spawnObstacleUnified;

    // KLUCZOWA ZMIANA: Nie zastępuj oryginalnej funkcji generateAtheroscleroticPlaques
    // Zamiast tego, udostępnij zunifikowaną wersję jako alternatywę
    window.generateAtheroscleroticPlaquesUnified = generateAtheroscleroticPlaquesUnified;

    window.calculatePositionInTunnel = calculateUnifiedPositionInTunnel;

    // Upewnij się, że animacja jest włączona
    window.animationEnabled = true;

    // Dodaj cleanup co 10 sekund
    setInterval(() => {
        if (window.unifiedObjectManager) {
            window.unifiedObjectManager.cleanup();
        }
    }, 10000);

    // Dodaj raportowanie co 30 sekund (debug)
    if (DEBUG_MODE) {
        setInterval(() => {
            if (window.unifiedObjectManager) {
                console.log("📊 Unified Object System Status:", window.unifiedObjectManager.getStatus());
            }
        }, 30000);
    }

    console.log("✅ Zunifikowany system zarządzania obiektami zainicjalizowany");

    return true;
}

// Eksportuj funkcje
window.UnifiedObjectManager = UnifiedObjectManager;
window.calculateUnifiedPositionInTunnel = calculateUnifiedPositionInTunnel;
window.spawnEggUnified = spawnEggUnified;
window.spawnObstacleUnified = spawnObstacleUnified;
window.generateAtheroscleroticPlaquesUnified = generateAtheroscleroticPlaquesUnified;
window.generatePlaqueClusterUnified = generatePlaqueClusterUnified;
window.createPlaqueUnified = createPlaqueUnified;
window.initializeUnifiedObjectSystem = initializeUnifiedObjectSystem;

// Auto-inicjalizacja
if (typeof window !== 'undefined') {
    // Dodaj do kolejki inicjalizacji
    if (typeof window.initQueue === 'undefined') {
        window.initQueue = [];
    }

    window.initQueue.push(() => {
        initializeUnifiedObjectSystem();
        console.log("🎯 Zunifikowany system obiektów zastosowany automatycznie");
    });

    // Jeśli gra już działa, zastosuj natychmiast
    if (typeof gameRunning !== 'undefined' && gameRunning) {
        initializeUnifiedObjectSystem();
    }
}
