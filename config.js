// --- START OF FILE config.js ---

const Config = {
    // --- Base Values for Level Scaling ---
    INITIAL_BASE_SPEED: 0.015,          // <PERSON><PERSON><PERSON><PERSON> wolnie<PERSON><PERSON>y start dla Level 1
    INITIAL_MIN_SPEED_MULTIPLIER: 0.4, // <PERSON><PERSON><PERSON><PERSON> wolniejszy start dla Level 1
    INITIAL_EGG_SPAWN_INTERVAL: 3500,  // Zmniejszono dla większej intensywności
    INITIAL_OBSTACLE_SPAWN_INTERVAL: 3000, // Zmniejszono dla większej intensywności
    INITIAL_WALL_CUBE_SPAWN_INTERVAL: 2800, // Zmniejszono dla większej intensywności
    INITIAL_RED_BLOOD_CELL_SPAWN_INTERVAL: 600, // Zmniejszono dla większej intensywności
    BASE_TUNNEL_SEGMENTS: 180,        // Reduced starting length for faster initialization
    SEGMENTS_PER_LEVEL: 40,           // Reduced segments per level

    // --- Current Scaled Values (modified by gameLogic.js) ---
    // These will be calculated in startGame based on gameLevel
    BASE_SPEED: 0.035,                // Will be recalculated
    MIN_SPEED_MULTIPLIER: 0.7,      // Will be recalculated
    EGG_SPAWN_INTERVAL_INITIAL: 10, // Zmniejszono dla większej intensywności
    OBSTACLE_SPAWN_INTERVAL_INITIAL: 25, // Zmniejszono dla większej intensywności
    WALL_CUBE_SPAWN_INTERVAL_INITIAL: 8, // Zmniejszono dla większej intensywności
    RED_BLOOD_CELL_SPAWN_INTERVAL_INITIAL: 120, // Zmniejszono dla większej intensywności
    TUNNEL_SEGMENTS: 180,           // Will be recalculated (reduced for faster startup)

    // --- Other Config (potentially adjusted) ---
    BUNNY_LATERAL_SPEED: 0.08, // Reduced from 0.12 to make movement less sensitive
    BUNNY_VERTICAL_SPEED: 0.07, // Reduced from 0.10 to make movement less sensitive
    MAX_SPEED_MULTIPLIER: 4.5,       // Max speed cap remains high
    MAX_BASE_SPEED: 0.12,            // Max base speed can increase slightly more
    BASE_SPEED_INCREASE: 0.0025,     // Rate of increase *within* a level (slightly slower)
    DIFFICULTY_INCREASE_INTERVAL: 8000, // Slower interval for intra-level increase

    OBJECT_REMOVAL_DISTANCE: 120,
    EGG_SPAWN_MIN_DIST: 15,
    EGG_SPAWN_MAX_DIST: 35,
    OBSTACLE_SPAWN_MIN_DIST: 15,
    OBSTACLE_SPAWN_MAX_DIST: 45,
    WALL_CUBE_SPAWN_MIN_DIST: 10,
    WALL_CUBE_SPAWN_MAX_DIST: 50,

    // --- Remaining Config (mostly unchanged) ---
    // ... (Shooting, Fleeing Eggs, Gameplay Points/Energy, Tunnel Diameters, Sound, Camera, World Factors, Wall Cube Details, Physics) ...
    MAX_AMMO: 50,
    SHOT_COOLDOWN: 70,
    AMMO_REGEN_INTERVAL: 1000,
    LASER_SPEED: 250,
    LASER_TTL: 3000, // Reduced from 5000 to 3000 to prevent laser from shooting too far
    FLEEING_EGG_COLORS: ["#2ecc71", "#3498db"],
    EGG_FLEE_SPEED: 0.15,
    EGG_FLEE_DETECTION_RADIUS: 8.0,
    EGG_FLEE_DURATION: 1500, // Duration in milliseconds that eggs will flee before stopping (1.5 seconds)
    INITIAL_ENERGY: 100,
    ENERGY_PER_EGG: 8,
    ENERGY_LOST_ON_COLLISION: 8, // Reduced from 10 to 8 to make obstacle collisions less punishing
    ENERGY_LOST_INDESTRUCTIBLE_BONUS: 8, // Reduced from 10 to 8 to make indestructible obstacle collisions less punishing

    // Modyfikatory energii w zależności od poziomu trudności
    ENERGY_MODIFIER_EASY: 0.3, // 30% bazowej wartości dla łatwych poziomów
    ENERGY_MODIFIER_MEDIUM: 0.5, // Reduced from 0.6 to 0.5 (50% of base value for medium levels)
    ENERGY_MODIFIER_HARD: 0.6, // Reduced from 0.8 to 0.6 (60% of base value for hard levels)
    POINTS_PER_EGG: 10,
    POINTS_PER_DESTRUCTION: 5,
    POINTS_LOST_PER_MISSED_EGG: 5,
    // These values will be overridden based on the current level's coronary segment diameter
    TUNNEL_DIAMETER_START: 5.0, // The default value will be replaced with coronary segment diameter
    TUNNEL_DIAMETER_MIN: 0.4,   // Minimum diameter for narrow sections
    TUNNEL_DIAMETER_END: 0.2,   // End diameter
    TUNNEL_NARROW_POINTS: [],
    HEARTBEAT_VOLUME: 0.6,
    SOUND_FADE_TIME: 0.2,
    CAMERA_RADIUS: 0.6,    // Zmniejszono z 0.8 do 0.6, aby przybliżyć kamerę do gracza
    CAMERA_HEIGHT_OFFSET: 0.3,
    CAMERA_ROTATION_OFFSET: 0,
    CAMERA_ACCELERATION: 0.5,
    CAMERA_MAX_SPEED: 30,
    CAMERA_FOV: 0.6,
    TUNNEL_SEGMENT_LENGTH: 5,
    TUNNEL_BASE_RADIUS: 2.5,
    TUNNEL_TURN_FACTOR: 0.04,
    TUNNEL_UPDOWN_FACTOR: 0.02,
    WALL_CUBE_RED_CHANCE: 0.85,
    WALL_CUBE_SIZE_MIN: 2.0,
    WALL_CUBE_SIZE_MAX: 6.0,
    WALL_CUBE_DETACH_MASS: 0.5,
    WALL_CUBE_ENERGY_PENALTY: 12, // Reduced from 18 to make level 3 more playable
    FRAGMENT_ENERGY_PENALTY: 3, // Reduced from 5 to 3 to make fragment collisions less punishing
    OBSTACLE_FRAGMENT_COUNT: 4,
    OBSTACLE_FRAGMENT_MIN_SIZE: 0.2,
    OBSTACLE_FRAGMENT_MAX_SIZE: 0.4,
    OBSTACLE_FRAGMENT_MASS: 0.3,
    OBSTACLE_FRAGMENT_IMPULSE: 1.5,
    GRAVITY: new BABYLON.Vector3(0, 0, 0),

    // Skalowanie obiektów w wąskich tunelach
    NARROW_TUNNEL_OBJECT_SCALE: 0.6, // Obiekty w wąskich tunelach będą mniejsze (60% normalnego rozmiaru)
    NARROW_TUNNEL_THRESHOLD: 2.0,    // Próg średnicy tunelu, poniżej którego tunel jest uznawany za wąski

    // Blood cell emitter positioning
    BLOOD_CELL_INITIAL_PHASE_DURATION: 10000, // Duration of initial phase in milliseconds (10 seconds)
    BLOOD_CELL_INITIAL_PHASE_MIN_DISTANCE: 20, // Minimum distance during initial phase
    BLOOD_CELL_INITIAL_PHASE_MAX_DISTANCE: 35, // Maximum distance during initial phase
    BLOOD_CELL_NORMAL_PHASE_MIN_DISTANCE: 25,  // Minimum distance during normal phase
    BLOOD_CELL_NORMAL_PHASE_MAX_DISTANCE: 60,  // Maximum distance during normal phase

    useObjectPooling: true,
    useSceneManager: true,
    useTextureCompression: false, // Wyłączone, aby testować ładowanie tekstur JPG
    useInstancedMeshes: true,
    useThinInstances: true,
    freezeStaticMeshes: true,
    useHardwareScaling: true,
    hardwareScalingLevel: 1.0, // 1.0 = brak skalowania, większe wartości = niższa rozdzielczość
    usePostprocessingOptimization: true,

    // Odległość, przy której obiekty są ukrywane
    cullDistance: 100,
    // Odległość, przy której jakość obiektów jest obniżana
    lodDistance: 50,
    // Interwał aktualizacji optymalizacji (ms)
    updateInterval: 500,
    // Maksymalna liczba obiektów przetwarzanych w jednej aktualizacji
    maxObjectsPerUpdate: 100
};



// Only log if DEBUG_MODE is explicitly true
if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
    console.log("Config loaded (DEBUG MODE) - Increased speed and object density.");
}
// --- END OF FILE config.js ---
