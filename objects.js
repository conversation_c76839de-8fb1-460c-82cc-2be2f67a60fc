// --- START OF FILE objects.js (Unified Compatible Version) ---

// Initialize the object system
function initializeObjectSystem() {
    console.log("Initializing object system...");

    // Initialize unified object system first
    if (typeof initializeUnifiedObjectSystem === 'function') {
        initializeUnifiedObjectSystem();
        console.log("✅ Unified object system initialized");
    } else {
        console.warn("⚠️ Unified object system not available, using fallback mode");
    }

    // Initialize any necessary variables or systems
    if (typeof initObjectPools === 'function') {
        initObjectPools();
    }

    // Make the function available globally
    window.initializeObjectSystem = initializeObjectSystem;

    return true;
}

// Update lasers position and check for collisions
function updateLasers(deltaTime) {
    if (!lasers || !scene || !gameRunning) return;

    // Update each laser's position
    for (let i = lasers.length - 1; i >= 0; i--) {
        const laser = lasers[i];

        // Skip invalid lasers
        if (!laser || !laser.mesh || laser.mesh.isDisposed()) {
            lasers.splice(i, 1);
            continue;
        }

        // Move laser forward
        const moveDistance = laser.speed * deltaTime;
        const moveVector = laser.direction.scale(moveDistance);
        laser.mesh.position.addInPlace(moveVector);

        // Check if laser has existed too long
        const now = Date.now();
        if (now - laser.creationTime > Config.LASER_TTL) {
            // Return to pool if using object pooling
            if (typeof poolManager !== 'undefined' && poolManager.returnObject) {
                laser.mesh.setEnabled(false);

                poolManager.returnObject("laser", laser);
            } else {

                laser.mesh.dispose();
            }
            lasers.splice(i, 1);
        }
    }

    // Check for collisions with obstacles
    checkLaserObstacleCollisions();

    // Make the function available globally
    window.updateLasers = updateLasers;
}

// Check for collisions between lasers and obstacles
function checkLaserObstacleCollisions() {
    if (!lasers || !obstacles || !scene || !gameRunning) return;

    for (let i = lasers.length - 1; i >= 0; i--) {
        const laser = lasers[i];
        if (!laser || !laser.mesh || laser.mesh.isDisposed()) continue;

        for (let j = obstacles.length - 1; j >= 0; j--) {
            const obstacle = obstacles[j];
            if (!obstacle || !obstacle.mesh || obstacle.mesh.isDisposed()) continue;

            // Skip indestructible obstacles
            if (!obstacle.isDestructible) continue;

            // Simple distance-based collision detection
            const distance = BABYLON.Vector3.Distance(
                laser.mesh.position,
                obstacle.mesh.position
            );

            // If collision detected
            if (distance < 1.0) {
                // Play sound and particle effect
                if (typeof playSoundEffect === 'function') {
                    playSoundEffect(destructionSound);
                }

                if (typeof playParticleEffect === 'function') {
                    playParticleEffect("obstacleDestruction", obstacle.mesh.position);
                }

                // Add points
                score += Config.POINTS_PER_DESTRUCTION;

                // Remove obstacle
                obstacle.mesh.dispose();
                obstacles.splice(j, 1);

                // Remove laser
                if (typeof poolManager !== 'undefined' && poolManager.returnObject) {
                    laser.mesh.setEnabled(false);

                    poolManager.returnObject("laser", laser);
                } else {

                    laser.mesh.dispose();
                }
                lasers.splice(i, 1);

                // Update HUD
                if (typeof updateHUD === 'function') {
                    updateHUD();
                }

                break; // Laser hit something, move to next laser
            }
        }
    }
}

// Generate initial objects at the start of the game
function generateInitialObjects() {
    console.log("Generating initial objects...");

    // Use setTimeout to spread object creation over time to prevent freezing
    let objectsCreated = 0;
    const totalObjects = 8; // 5 eggs + 3 obstacles

    function createNextObject() {
        if (objectsCreated >= totalObjects) {
            console.log("Initial objects generation completed");
            return;
        }

        try {
            if (objectsCreated < 5) {
                // Generate eggs first
                spawnEgg();
            } else {
                // Then generate obstacles
                spawnObstacle();
            }
            objectsCreated++;

            // Schedule next object creation with small delay
            setTimeout(createNextObject, 50); // 50ms delay between objects
        } catch (error) {
            console.error("Error creating initial object:", error);
            objectsCreated++;
            setTimeout(createNextObject, 100); // Longer delay on error
        }
    }

    // Start the creation process
    createNextObject();

    // Make the function available globally
    window.generateInitialObjects = generateInitialObjects;

    return true;
}

// Generate initial wall cubes at the start of the game
function generateInitialWallCubes() {
    console.log("Generating initial wall cubes...");

    // Use setTimeout to spread wall cube creation over time to prevent freezing
    let cubesCreated = 0;
    const totalCubes = 3;

    function createNextCube() {
        if (cubesCreated >= totalCubes) {
            console.log("Initial wall cubes generation completed");
            return;
        }

        try {
            spawnWallCube();
            cubesCreated++;

            // Schedule next cube creation with small delay
            setTimeout(createNextCube, 75); // 75ms delay between cubes
        } catch (error) {
            console.error("Error creating initial wall cube:", error);
            cubesCreated++;
            setTimeout(createNextCube, 150); // Longer delay on error
        }
    }

    // Start the creation process
    createNextCube();

    // Make the function available globally
    window.generateInitialWallCubes = generateInitialWallCubes;

    return true;
}

// Helper function to calculate a position in the tunnel
function calculatePositionInTunnel(targetZ) {
    if (!tunnelSections || tunnelSections.length === 0) return null;

    // Find the section at the target Z position
    const section = findSectionAtPosition(targetZ);
    if (!section) return null;

    // Calculate a random position within the section
    const angle = Math.random() * Math.PI * 2;
    const radius = section.radius * Math.random() * 0.7; // Stay away from walls

    // Add random Z-offset for 3D effect (±30% of section radius)
    const zOffset = (Math.random() * 2 - 1) * section.radius * 0.3;

    return new BABYLON.Vector3(
        section.centerPoint.x + Math.cos(angle) * radius,
        section.centerPoint.y + Math.sin(angle) * radius,
        targetZ + zOffset
    );
}

// Throttling variables for spawn functions
let lastEggSpawn = 0;
let lastObstacleSpawn = 0;
let lastWallCubeSpawn = 0;
const SPAWN_THROTTLE_MS = 100; // Minimum time between spawns

// Spawn an egg at a calculated Z position
function spawnEgg() {
    // Throttle spawning to prevent performance issues
    const now = performance.now();
    if (now - lastEggSpawn < SPAWN_THROTTLE_MS) return;
    lastEggSpawn = now;

    if (!bunnyCollider || !tunnelSections || tunnelSections.length === 0 || !scene) return;

    // Calculate spawn position
    const distance = Config.EGG_SPAWN_MIN_DIST + Math.random() * (Config.EGG_SPAWN_MAX_DIST - Config.EGG_SPAWN_MIN_DIST);
    const targetZ = bunnyCollider.position.z - distance;

    // Get position in tunnel
    const position = calculatePositionInTunnel(targetZ);
    if (!position) return;

    // Find section for size reference
    const section = findSectionAtPosition(targetZ);

    // Create egg mesh with adjusted diameter
    const diameter = section ? section.radius * 0.18 : 0.32;
    const egg = BABYLON.MeshBuilder.CreateSphere("egg", {
        diameter: diameter,
        segments: 12
    }, scene);

    // Position egg
    egg.position = position;

    // Create material
    const eggMaterial = new BABYLON.StandardMaterial("eggMat", scene);
    eggMaterial.diffuseColor = new BABYLON.Color3(1, 0.8, 0.2); // Gold color
    eggMaterial.emissiveColor = new BABYLON.Color3(0.5, 0.4, 0.1);
    egg.material = eggMaterial;

    // Create light
    const eggLight = new BABYLON.PointLight("eggLight", position, scene);
    eggLight.diffuse = new BABYLON.Color3(1, 0.8, 0.2);
    eggLight.intensity = 0.6;
    eggLight.range = 3;

    // Add to eggs array
    eggs.push({
        mesh: egg,
        light: eggLight,
        returnToCenter: false
    });

    // Make the function available globally
    window.spawnEgg = spawnEgg;
}

// Spawn an obstacle at a calculated Z position
function spawnObstacle() {
    // Throttle spawning to prevent performance issues
    const now = performance.now();
    if (now - lastObstacleSpawn < SPAWN_THROTTLE_MS) return;
    lastObstacleSpawn = now;

    if (!bunnyCollider || !tunnelSections || tunnelSections.length === 0 || !scene) return;

    // Calculate spawn position
    const distance = Config.OBSTACLE_SPAWN_MIN_DIST + Math.random() * (Config.OBSTACLE_SPAWN_MAX_DIST - Config.OBSTACLE_SPAWN_MIN_DIST);
    const targetZ = bunnyCollider.position.z - distance;

    // Get position in tunnel
    const position = calculatePositionInTunnel(targetZ);
    if (!position) return;

    // Find section for size reference
    const section = findSectionAtPosition(targetZ);

    // Create obstacle mesh with adjusted diameter
    const diameter = section ? section.radius * 0.22 : 0.38;
    const obstacle = BABYLON.MeshBuilder.CreateSphere("obstacle", {
        diameter: diameter,
        segments: 16
    }, scene);

    // Position obstacle
    obstacle.position = position;

    // Create material
    const obstacleMaterial = new BABYLON.StandardMaterial("obstacleMat", scene);
    obstacleMaterial.diffuseColor = new BABYLON.Color3(0.9, 0.3, 0.3); // Red color
    obstacleMaterial.emissiveColor = new BABYLON.Color3(0.4, 0.1, 0.1);
    obstacle.material = obstacleMaterial;

    // Create light
    const obstacleLight = new BABYLON.PointLight("obstacleLight", position, scene);
    obstacleLight.diffuse = new BABYLON.Color3(0.9, 0.3, 0.3);
    obstacleLight.intensity = 0.7;
    obstacleLight.range = 4;

    // Add physics if available
    if (typeof BABYLON.PhysicsImpostor !== 'undefined') {
        obstacle.physicsImpostor = new BABYLON.PhysicsImpostor(
            obstacle,
            BABYLON.PhysicsImpostor.SphereImpostor,
            { mass: 0.1, restitution: 0.2 },
            scene
        );
    }

    // Add to obstacles array
    obstacles.push({
        mesh: obstacle,
        light: obstacleLight,
        isDestructible: Math.random() > 0.2 // 80% chance of being destructible
    });

    // Make the function available globally
    window.spawnObstacle = spawnObstacle;
}

// Spawn a wall cube cluster at a calculated Z position
function spawnWallCubeCluster(clusterSize = 5, clusterSpacing = 0.7) {
    if (!bunnyCollider || !tunnelSections || tunnelSections.length === 0 || !scene) return;

    // Calculate spawn position (central for cluster)
    const distance = Config.WALL_CUBE_SPAWN_MIN_DIST + Math.random() * (Config.WALL_CUBE_SPAWN_MAX_DIST - Config.WALL_CUBE_SPAWN_MIN_DIST);
    const targetZ = bunnyCollider.position.z - distance;
    const section = findSectionAtPosition(targetZ);
    if (!section) return;

    // Central angle for cluster
    const baseAngle = Math.random() * Math.PI * 2;
    const baseRadius = section.radius * (0.82 - Math.random() * 0.08); // closer to wall

    for (let i = 0; i < clusterSize; i++) {
        // Honeycomb arrangement (hex)
        const angleOffset = (i === 0) ? 0 : (Math.PI * 2 / 6) * ((i - 1) % 6);
        const ring = Math.floor((i - 1) / 6) + 1;
        const angle = baseAngle + angleOffset;
        const offsetRadius = baseRadius - ring * clusterSpacing * section.radius * 0.12;

        // Position on the wall
        const wallNormal = new BABYLON.Vector3(Math.cos(angle), Math.sin(angle), 0).normalize();

        // Add random Z-offset for 3D effect (±30% of section radius)
        const zOffset = (Math.random() * 2 - 1) * section.radius * 0.3;

        const position = new BABYLON.Vector3(
            section.centerPoint.x + wallNormal.x * offsetRadius,
            section.centerPoint.y + wallNormal.y * offsetRadius,
            targetZ + zOffset
        );

        // Size and irregularity
        const patchSize = section.radius * (0.12 + Math.random() * 0.07);
        const plaque = BABYLON.MeshBuilder.CreateDisc("wallPlaque", {
            radius: patchSize,
            tessellation: 12 + Math.floor(Math.random() * 8)
        }, scene);
        if (plaque && plaque.getVerticesData) {
            const positions = plaque.getVerticesData(BABYLON.VertexBuffer.PositionKind);
            for (let v = 0; v < positions.length; v += 3) {
                const scale = 0.8 + Math.random() * 0.4;
                positions[v] *= scale;
                positions[v + 1] *= scale;
            }
            plaque.updateVerticesData(BABYLON.VertexBuffer.PositionKind, positions);
        }
        plaque.position = position;
        plaque.lookAt(section.centerPoint);

        // Material
        const plaqueMaterial = new BABYLON.StandardMaterial("plaqueMat", scene);
        const type = atheroscleroticTypes[Math.floor(Math.random() * atheroscleroticTypes.length)];
        if (type.texturePath) {
            plaqueMaterial.diffuseTexture = new BABYLON.Texture(type.texturePath, scene);
            plaqueMaterial.diffuseTexture.hasAlpha = true;
        }
        plaqueMaterial.alpha = 0.97;
        plaqueMaterial.emissiveColor = new BABYLON.Color3(0.4, 0.08, 0.08);
        plaqueMaterial.diffuseColor = new BABYLON.Color3(0.7, 0.1, 0.1);
        plaque.material = plaqueMaterial;

        if (typeof wallPlaques === 'undefined') {
            window.wallPlaques = [];
        }
        wallPlaques.push({
            mesh: plaque,
            isRed: false,
            isDetached: false
        });
    }

    window.spawnWallCubeCluster = spawnWallCubeCluster;
}

// Spawn a wall cube at a calculated Z position
function spawnWallCube() {
    // Throttle spawning to prevent performance issues
    const now = performance.now();
    if (now - lastWallCubeSpawn < SPAWN_THROTTLE_MS) return;
    lastWallCubeSpawn = now;

    spawnWallCubeCluster(7, 0.7);
    window.spawnWallCube = spawnWallCube;
}

// --- SYSTEM BLASZEK MIAŻDŻYCOWYCH (AHA) ---
// PLAQUE_TYPES is now available globally from atheroscleroticPlaques.js

// Funkcja wyświetlająca informację edukacyjną o typach przeszkód
function showPlaqueInfo() {
    if (window.ui && typeof window.ui.showMessage === 'function') {
        window.ui.showMessage(
            'Nowe przeszkody: blaszki miażdżycowe!\n' +
            'Typy 1-3 można niszczyć strzałem, typy 4-6 są nie do zniszczenia i należy je omijać.\n' +
            'Każdy typ ma inny wygląd i wpływ na rozgrywkę. Zwracaj uwagę na kolor i kształt!'
        );
    } else {
        // Fallback: jeśli UI nie jest gotowe, nie pokazuj alertu
    }
}
window.showPlaqueInfo = showPlaqueInfo;

/**
 * Ulepsza funkcje tworzenia obiektów, aby zapewnić, że obiekty są zawsze wewnątrz tunelu
 */
function enhanceObjectCreation() {
    // Ulepsz funkcję spawnEgg
    if (typeof window.spawnEgg === 'function') {
        const originalSpawnEgg = window.spawnEgg;
        window.spawnEgg = function() {
            const egg = originalSpawnEgg.apply(this, arguments);
            if (egg && egg.mesh) {
                // Upewnij się, że jajko jest w tunelu
                if (!isPositionInTunnel(egg.mesh.position)) {
                    egg.mesh.position = constrainPositionToTunnel(egg.mesh.position, 0.85);
                }
            }
            return egg;
        };
    }

    // Ulepsz funkcję spawnObstacle
    if (typeof window.spawnObstacle === 'function') {
        const originalSpawnObstacle = window.spawnObstacle;
        window.spawnObstacle = function() {
            const obstacle = originalSpawnObstacle.apply(this, arguments);
            if (obstacle && obstacle.mesh) {
                // Upewnij się, że przeszkoda jest w tunelu
                if (!isPositionInTunnel(obstacle.mesh.position)) {
                    obstacle.mesh.position = constrainPositionToTunnel(obstacle.mesh.position, 0.8);
                }
            }
            return obstacle;
        };
    }
}

// Wywołaj funkcję ulepszającą tworzenie obiektów
enhanceObjectCreation();

// Make all functions available globally
window.initializeObjectSystem = initializeObjectSystem;
window.updateLasers = updateLasers;
window.generateInitialObjects = generateInitialObjects;
window.generateInitialWallCubes = generateInitialWallCubes;
window.spawnEgg = spawnEgg;
window.spawnObstacle = spawnObstacle;
window.spawnWallCube = spawnWallCube;
window.spawnWallCubeCluster = spawnWallCubeCluster;
window.calculatePositionInTunnel = calculatePositionInTunnel;
window.showPlaqueInfo = showPlaqueInfo;
window.enhanceObjectCreation = enhanceObjectCreation;

// --- END OF FILE objects.js ---
