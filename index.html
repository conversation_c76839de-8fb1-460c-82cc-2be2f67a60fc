<!DOCTYPE html>
<html lang="en">

<head>
    <title><PERSON> Tunnel Lvl Scaling</title>
    <meta charset="UTF-8">
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <!-- Security headers -->
    <meta content="nosniff" http-equiv="content-type">
    <meta content="1; mode=block" http-equiv="x-ua-compatible">
    <meta content="no-referrer-when-downgrade" http-equiv="refresh">
    <!-- Cache control -->
    <meta content="max-age=86400, public" http-equiv="refresh">
    <!-- Add a version for cache busting -->
    <meta content="1.0.1" name="version">
    <link href="style.css?v=1.0.1" rel="stylesheet">
    <link href="https://hemolens.you2.pl/wp-content/uploads/2025/04/cropped-ddb6e75da7d245bd914da2d68153b05e8c808533-scaled-1-32x32.jpg"
          rel="icon"
          sizes="32x32">
    <link href="https://hemolens.you2.pl/wp-content/uploads/2025/04/cropped-ddb6e75da7d245bd914da2d68153b05e8c808533-scaled-1-192x192.jpg"
          rel="icon"
          sizes="192x192">
    <link href="https://hemolens.you2.pl/wp-content/uploads/2025/04/cropped-ddb6e75da7d245bd914da2d68153b05e8c808533-scaled-1-180x180.jpg"
          rel="apple-touch-icon">
    <!-- Azure Web PubSub Client -->

    <!-- Local copy if CDN fails -->
    <script>
        // Check if WebPubSubClient is defined after loading from CDN
        window.addEventListener('load', function () {
            if (typeof WebPubSubClient === 'undefined') {
                console.log('Loading Azure Web PubSub Client from local copy...');
                // Try multiple possible paths to find the file
                const possiblePaths = [
                    './azure/web-pubsub-client.js?v=1.0.1',
                    '/azure/web-pubsub-client.js?v=1.0.1',
                    '/assets/web-pubsub-client.js?v=1.0.1',
                    '/assets/azure/web-pubsub-client.js?v=1.0.1'
                ];

                function tryLoadScript(index) {
                    if (index >= possiblePaths.length) {
                        console.error('Failed to load Azure Web PubSub Client from all possible paths');
                        // Create a mock implementation as last resort
                        return;
                    }

                    let script = document.createElement('script');
                    script.type = 'application/javascript';
                    script.src = possiblePaths[index];
                    console.log('Trying to load from:', script.src);

                    script.onload = function () {
                        console.log('Local Azure Web PubSub Client loaded successfully from', script.src);
                        window.dispatchEvent(new Event('WebPubSubClientLoaded'));
                    };

                    script.onerror = function () {
                        console.warn('Failed to load from', script.src, ', trying next path...');
                        tryLoadScript(index + 1);
                    };

                    document.head.appendChild(script);
                }

                tryLoadScript(0);
            }
        });
    </script>
    <!-- We're dynamically loading the script, so preload is not needed -->
</head>

<body>

<canvas id="gameCanvas" style="width: 100%; height: 100%; position: absolute; top: 0; left: 0;"></canvas>
<!-- Overlays -->
<div class="overlay" id="loadingScreen">
    <div class="overlay-content">
        <h1>Loading...</h1>
        <div id="loadingText">Initializing...</div>
        <div class="progress-bar-container">
            <div id="progressBar"></div>
        </div>
    </div>
</div>

<div class="overlay" id="levelListScreen" style="display: none;">
    <div class="overlay-content">
        <h1>Select Level</h1>
        <div class="level-list" id="levelList">
            <!-- Level items will be added dynamically -->
        </div>
        <button id="backToStartBtn">Back</button>
    </div>
</div>

<!-- Info Screen -->
<div class="overlay" id="infoScreen" style="display: none;">
    <div class="overlay-content">
        <h1>Game Information</h1>
        <div class="game-info-content" id="gameInfoContent"></div>
        <button id="backFromInfoBtn">Back</button>
    </div>
</div>

<!-- Add this to the settings screen HTML -->
<!-- Add this to index.html for the complete settings screen -->

<!-- Main Settings Screen -->
<div class="overlay" id="settingsScreen" style="display: none;">
    <div class="overlay-content">
        <h1>Game Settings</h1>

        <div class="settings-container">
            <!-- Game Settings Section -->
            <div class="setting-group">
                <h2>Audio & Visual</h2>

                <div class="setting-option">
                    <label>
                        <input id="effectsEnabled" type="checkbox"> Enable Visual Effects
                    </label>
                </div>

                <div class="setting-option">
                    <label>
                        <input id="musicEnabled" type="checkbox"> Enable Background Music
                    </label>
                </div>

                <div class="setting-option">
                    <label>
                        <input id="learningModeEnabled" type="checkbox"> Enable Learning Mode
                    </label>
                    <p class="setting-description">When enabled, the game will ask short educational questions during gameplay.</p>
                </div>

                <div class="setting-option" id="musicSelection">
                    <h3>Background Music Track</h3>
                    <label>
                        <input name="musicTrack" type="radio" value="random"> Random Track
                    </label>
                    <label>
                        <input name="musicTrack" type="radio" value="music1"> Blood Flow
                    </label>
                    <label>
                        <input name="musicTrack" type="radio" value="music2"> Heartbeat
                    </label>
                    <label>
                        <input name="musicTrack" type="radio" value="music3"> Artery Journey
                    </label>
                    <label>
                        <input name="musicTrack" type="radio" value="music4"> Deep Pulse
                    </label>
                </div>
            </div>

            <!-- Multiplayer Settings Section - Integrated -->
            <div class="setting-group">
                <h2>Multiplayer</h2>

                <div class="setting-option">
                    <label>
                        <input id="allowJoining" type="checkbox"> Allow Other Players to Join
                    </label>
                    <p class="setting-description">When enabled, other players can invite you to their games and vice
                        versa.</p>
                </div>

                <div class="setting-option">
                    <label for="playerUsername">Your Username:</label>
                    <input id="playerUsername" placeholder="Enter your username" type="text">
                    <p class="setting-description">This name will be displayed to other players.</p>
                </div>

                <div class="setting-option">
                    <label>Player Color:</label>
                    <div class="color-selector">
                        <div class="color-option" data-color="blue" style="background-color: rgb(77, 77, 255);"></div>
                        <div class="color-option" data-color="red" style="background-color: rgb(255, 77, 77);"></div>
                        <div class="color-option" data-color="green" style="background-color: rgb(77, 255, 77);"></div>
                        <div class="color-option" data-color="yellow"
                             style="background-color: rgb(255, 255, 77);"></div>
                        <div class="color-option" data-color="purple"
                             style="background-color: rgb(255, 77, 255);"></div>
                        <div class="color-option" data-color="cyan" style="background-color: rgb(77, 255, 255);"></div>
                        <div class="color-option" data-color="orange"
                             style="background-color: rgb(255, 153, 77);"></div>
                        <div class="color-option" data-color="pink" style="background-color: rgb(255, 153, 204);"></div>
                    </div>
                </div>

                <div class="player-status">
                    <div id="connectionStatus">Not connected to multiplayer server</div>
                    <div id="playersOnline">Players online: <span id="onlineCount">0</span></div>
                </div>
            </div>

            <!-- Controls Section -->
            <div class="setting-group">
                <h2>Controls</h2>

                <div class="control-info">
                    <h3>Keyboard</h3>
                    <ul>
                        <li><strong>Movement:</strong> Arrow Keys or WASD</li>
                        <li><strong>Shoot:</strong> Space Bar</li>
                        <li><strong>Pause:</strong> P or Escape</li>
                    </ul>

                    <h3>Mobile</h3>
                    <ul>
                        <li><strong>Movement:</strong> Virtual Joystick (left side)</li>
                        <li><strong>Shoot:</strong> Shoot Button (right side)</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="settings-buttons">
            <button id="saveSettingsBtn">Save Settings</button>
            <button id="backFromSettingsBtn">Back</button>
        </div>
    </div>
</div>

<!-- Start Screen (Fallback) -->
<div class="overlay" id="startScreen" style="display: none;">
    <div class="overlay-content">
        <h1>Bunny Tunnel</h1>
        <p>Navigate the microscopic world!</p>
        <div class="intro-message" id="introMessage">
            <!-- Content populated by ui.js -->
        </div>
        <button id="startGameBtn">Start Game</button>
        <button id="levelSelectBtn">Level Select</button>
        <button id="infoBtn">Information</button>
        <button id="settingsBtn">Settings</button>
        <button id="soundControl">🔊</button>

        <!-- Available Games Section -->
        <div id="availableGamesSection" style="margin-top: 20px; display: none;">
            <h3>Available Games</h3>
            <div id="availableGamesList" style="max-height: 150px; overflow-y: auto; margin-bottom: 10px;">
                <!-- Games will be listed here -->
                <div class="no-games-message">No games available</div>
            </div>
            <button id="refreshGamesBtn">Refresh Games</button>
            <button id="joinGameBtn" disabled>Join Selected Game</button>
        </div>
    </div>
</div>


<div class="overlay" id="gameOverScreen" style="display: none;">
    <div class="overlay-content">
        <h1>Game Over</h1>
        <!-- *** ADDED: Level Info *** -->
        <p>Reached Level: <span id="gameOverLevel">1</span></p>
        <p>Final Score: <span id="finalScore">0</span></p>
        <p>High Score: <span id="highScore">0</span></p>
        <div class="button-group">
            <button id="restartBtn">Retry Level</button>
            <button id="nextLevelBtn">Next Level</button>
        </div>
        <button id="levelSelectFromGameOverBtn">Level Select</button>
    </div>
</div>

<!-- HUD -->
<div id="hud">
    <!-- *** ADDED: Level Display *** -->
    <div>Level: <span id="level">1</span></div>
    <div>Score: <span id="score">0</span></div>
    <div>High Score: <span id="hudHighScore">0</span></div>
    <div>Energy: <span id="energy">100</span>
        <div class="energy-bar-container">
            <div id="energyBarFill"></div>
        </div>
    </div>
    <div>Ammo: <span id="ammo">10</span></div>
    <div>Speed: <span id="speed">1.0x</span></div>
</div>

<canvas height="150" id="minimap" width="150"></canvas>
<div id="diagnostics"></div>
<!-- Removed renderCanvas as it's not used and might cause conflicts -->

<!-- === KONTROLKI MOBILNE === -->
<div id="mobileControls">
    <div id="moveControls">
        <div id="joystickContainer">
            <div id="joystickBase">
                <div id="joystickHandle"></div>
            </div>
        </div>
    </div>
    <div id="actionControls">
        <button class="action-btn" id="shootBtn">💥</button>
    </div>
</div>
<!-- ========================= -->

<!-- Scripts (ensure correct order) -->
<script src="https://cdn.babylonjs.com/babylon.js?v=1.0.1"></script>
<script src="https://cdn.babylonjs.com/loaders/babylonjs.loaders.min.js?v=1.0.1"></script>
<script src="https://cdn.babylonjs.com/cannon.js?v=1.0.1"></script>
<script src="https://cdn.babylonjs.com/gui/babylon.gui.min.js?v=1.0.1"></script>


<!-- Ładujemy globalne zmienne i ustawienia -->
<script src="globals.js"></script>
<script src="config.js"></script>
<script src="errorHandling.js"></script>
<script src="ffrCalculations.js"></script>
<script src="sceneManager.js"></script>
<script src="gameState.js"></script>
<script src="objectPool.js"></script>

<!-- Efekty i dźwięk -->
<script src="effects.js"></script>
<script src="audio.js"></script>

<!-- Interfejs i wejście -->
<script src="input.js"></script>
<script src="demoScene.js"></script>
<script src="ui.js"></script>
<script src="uiMessages.js"></script>

<!-- Logika obiektów gry -->
<script src="bloodCells.js"></script>
<script src="bloodEmitterSystem.js"></script>
<script src="detachWallCubes.js"></script>
<script src="pauseEducation.js"></script>
<script src="unifiedObjectSystem.js"></script>
<script src="objects.js"></script>
<script src="atheroscleroticPlaques.js"></script>
<script src="world.js"></script>
<script src="optimizedBloodSystem.js"></script>
<script src="gameLogic.js"></script>
<script src="gameOptimizer.js"></script>

<!-- Multiplayer i sieć -->
<script src="multiplayer.js"></script>
<script src="player.js"></script>

<!-- Główny skrypt inicjalizujący -->
<script src="main.js"></script>

<!-- This script is no longer needed as the next level button text is updated in ui.js -->
</body>

</html>
